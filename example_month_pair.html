<!DOCTYPE html>
<html>
<head>
  <title>Binance Data Collection</title>
  <link href="bootstrap.min.css" rel="stylesheet">
</head>
<body>
  <nav class="navbar navbar-dark bg-dark">
    <div class="container-md">
      <div class="row justify-content-between" style="width: 100%">
        <div class="col-sm">
          <a href="/"><img src="logo.png" height="30" class="d-inline-block align-top" alt=""></a>
          <span class="navbar-brand" style="padding-left: 10px;">Market Data</span>
        </div>
        <div class="col-sm" style="text-align: right" >
          <a href="https://github.com/binance/binance-public-data/" data-toggle="tooltip" title data-original-title="Public data document">
            <svg class="octicon octicon-mark-github v-align-middle" height="25" viewBox="0 0 16 16" version="1.1" width="25" aria-hidden="true" style="fill: #fff">
              <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </nav>
  <div class="container-md">
    <div id="navigation" class="h6" style="margin-top: .8rem"></div>

    <table class="table table-hover">
      <thead>
        <tr>
          <th scope="col">Item</th>
          <th scope="col">Size</th>
          <th scope="col">Last Modified</th>
        </tr>
      </thead>
      <tbody id="listing"></tbody>
    </table>
  </div>
  <script type="text/javascript" src="jquery.min.js"></script>
  <script type="text/javascript" src="bootstrap.bundle.min.js"></script>
  <script type="text/javascript">
    var S3BL_IGNORE_PATH = true;
    var BUCKET_URL = 'https://s3-ap-northeast-1.amazonaws.com/data.binance.vision';
    var BUCKET_WEBSITE_URL = 'https://data.binance.vision'
    var S3B_SORT = 'A2Z';
    var EXCLUDE_FILE = 'index.html'; 
    var S3B_ROOT_DIR = 'data/';
    $(function () {
      $('[data-toggle="tooltip"]').tooltip()
    })
  </script>
  <script type="text/javascript" src="xss.js"></script>
  <script type="text/javascript" src="list.js"></script>
</body>
</html>
